<!DOCTYPE html>
<html>
<head>
    <title>转盘角度计算测试</title>
</head>
<body>
    <h1>转盘角度计算测试</h1>
    <div id="results"></div>

    <script>
        function testWheelCalculation() {
            const totalPrizes = 6;
            const prizeAngle = 360 / totalPrizes; // 60°
            const results = [];

            // 测试每个奖品索引
            for (let prizeIndex = 0; prizeIndex < totalPrizes; prizeIndex++) {
                // 修复后的角度计算公式（考虑-90°偏移）
                const targetAngle = (360 - prizeIndex * prizeAngle) % 360;

                // 模拟基础旋转
                const baseRotation = 5 * 360; // 5圈
                const finalRotation = baseRotation + targetAngle;

                // 计算最终停止位置
                const normalizedFinalAngle = finalRotation % 360;
                const rotatedPositions = Math.round(normalizedFinalAngle / prizeAngle) % totalPrizes;
                const predictedPrizeIndex = (totalPrizes - rotatedPositions) % totalPrizes;
                
                results.push({
                    目标索引: prizeIndex,
                    目标角度: targetAngle + '°',
                    最终角度: finalRotation + '°',
                    归一化角度: normalizedFinalAngle + '°',
                    旋转位置数: rotatedPositions,
                    预测索引: predictedPrizeIndex,
                    是否匹配: prizeIndex === predictedPrizeIndex ? '✓' : '✗'
                });
            }

            return results;
        }

        // 运行测试
        const testResults = testWheelCalculation();
        const resultsDiv = document.getElementById('results');
        
        resultsDiv.innerHTML = '<h2>测试结果：</h2>';
        testResults.forEach((result, index) => {
            const div = document.createElement('div');
            div.style.margin = '10px 0';
            div.style.padding = '10px';
            div.style.border = '1px solid #ccc';
            div.style.backgroundColor = result.是否匹配 === '✓' ? '#e8f5e8' : '#ffe8e8';
            
            div.innerHTML = `
                <strong>测试 ${index + 1}:</strong><br>
                目标索引: ${result.目标索引}<br>
                目标角度: ${result.目标角度}<br>
                最终角度: ${result.最终角度}<br>
                归一化角度: ${result.归一化角度}<br>
                旋转位置数: ${result.旋转位置数}<br>
                预测索引: ${result.预测索引}<br>
                匹配结果: ${result.是否匹配}
            `;
            
            resultsDiv.appendChild(div);
        });

        // 特别测试索引5的情况
        console.log('特别测试索引5（参与奖）:');
        const prizeIndex = 5;
        const targetAngle = (360 - prizeIndex * 60) % 360; // (360 - 300) % 360 = 60°
        const baseRotation = 5 * 360; // 1800°
        const finalRotation = baseRotation + targetAngle; // 1860°
        const normalizedFinalAngle = finalRotation % 360; // 60°
        const rotatedPositions = Math.round(normalizedFinalAngle / 60) % 6; // 1
        const predictedPrizeIndex = (6 - rotatedPositions) % 6; // (6 - 1) % 6 = 5

        console.log({
            目标索引: prizeIndex,
            目标角度: targetAngle + '°',
            最终角度: finalRotation + '°',
            归一化角度: normalizedFinalAngle + '°',
            旋转位置数: rotatedPositions,
            预测索引: predictedPrizeIndex,
            是否匹配: prizeIndex === predictedPrizeIndex ? '✓ 成功' : '✗ 失败'
        });
    </script>
</body>
</html>
